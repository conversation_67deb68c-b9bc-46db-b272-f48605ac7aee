import React from 'react';
import { motion } from 'framer-motion';

const PublicGallery: React.FC = () => {
  // URLs de gatos y anime kawaii de Unsplash
  const images = [
    'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=600&fit=crop',
    'https://images.unsplash.com/photo-1573865526739-10659fec78a5?w=400&h=500&fit=crop',
    'https://images.unsplash.com/photo-1596854407944-bf87f6fdd49e?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1533738363-b7f9aef128ce?w=400&h=600&fit=crop',
    'https://images.unsplash.com/photo-1574158622682-e40e69881006?w=400&h=500&fit=crop',
    'https://images.unsplash.com/photo-1592194996308-7b43878e84a6?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=600&fit=crop',
    'https://images.unsplash.com/photo-1571566882372-1598d88abd90?w=400&h=500&fit=crop',
    'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=400&fit=crop',
  ];

  return (
    <section className="py-8 md:py-16 px-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="text-center mb-8 md:mb-12"
      >
        <h2 className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
          Galería Kawaii 🌸
        </h2>
        <p className="font-inter text-base md:text-lg text-white/90 max-w-2xl mx-auto">
          Una colección de momentos dulces y adorables
        </p>
      </motion.div>

      <div className="columns-1 sm:columns-2 lg:columns-3 gap-4 md:gap-6 space-y-4 md:space-y-6">
        {images.map((src, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
            whileHover={{ 
              scale: 1.05,
              boxShadow: '0 20px 40px rgba(255, 110, 161, 0.3)'
            }}
            className="break-inside-avoid bg-white/10 backdrop-blur-sm rounded-xl md:rounded-2xl overflow-hidden border border-white/20 cursor-pointer"
          >
            <img
              src={src}
              alt={`Kawaii ${index + 1}`}
              className="w-full h-auto object-cover"
              loading="lazy"
            />
            <div className="p-3 md:p-4">
              <p className="font-inter text-white/80 text-xs md:text-sm text-center">
                ✨ Momento mágico {index + 1}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default PublicGallery;