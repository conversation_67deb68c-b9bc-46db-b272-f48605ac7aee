import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Music, Play, Pause, Volume2, VolumeX, Minimize2, Maximize2 } from 'lucide-react';

const Header: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showPlayer, setShowPlayer] = useState(true);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-rosa-pastel/90 to-fucsia-suave/90 backdrop-blur-lg border-b border-white/20 shadow-lg"
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo/Title */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center gap-2"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <Music className="text-white w-6 h-6" />
            </motion.div>
            <h1 className="font-playfair text-lg md:text-xl font-bold text-white">
              𝑣𝑎𝑛𝑒𝑠𝑎 𝑚𝑎𝑐𝑎𝑟𝑒𝑛𝑎 ☽✯
            </h1>
          </motion.div>

          {/* Music Player Controls */}
          <AnimatePresence>
            {showPlayer && (
              <motion.div
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 100 }}
                className="flex items-center gap-2 md:gap-3"
              >
                {!isMinimized && (
                  <motion.div
                    initial={{ width: 0, opacity: 0 }}
                    animate={{ width: "auto", opacity: 1 }}
                    exit={{ width: 0, opacity: 0 }}
                    className="hidden md:flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 border border-white/30"
                  >
                    <span className="text-white text-sm font-medium">
                      🎵 Música que Vibra
                    </span>
                  </motion.div>
                )}

                {/* Control Buttons */}
                <div className="flex items-center gap-1 md:gap-2">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={togglePlay}
                    className="bg-white/20 backdrop-blur-sm p-2 rounded-full border border-white/30 text-white hover:bg-white/30 transition-all"
                  >
                    {isPlaying ? (
                      <Pause size={16} className="md:w-5 md:h-5" />
                    ) : (
                      <Play size={16} className="md:w-5 md:h-5" />
                    )}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleMute}
                    className="bg-white/20 backdrop-blur-sm p-2 rounded-full border border-white/30 text-white hover:bg-white/30 transition-all"
                  >
                    {isMuted ? (
                      <VolumeX size={16} className="md:w-5 md:h-5" />
                    ) : (
                      <Volume2 size={16} className="md:w-5 md:h-5" />
                    )}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleMinimize}
                    className="bg-white/20 backdrop-blur-sm p-2 rounded-full border border-white/30 text-white hover:bg-white/30 transition-all"
                  >
                    {isMinimized ? (
                      <Maximize2 size={16} className="md:w-5 md:h-5" />
                    ) : (
                      <Minimize2 size={16} className="md:w-5 md:h-5" />
                    )}
                  </motion.button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Expanded Music Player */}
        <AnimatePresence>
          {showPlayer && !isMinimized && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 overflow-hidden"
            >
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -20, opacity: 0 }}
                className="bg-white/10 backdrop-blur-lg rounded-xl p-3 border border-white/20"
              >
                <iframe 
                  style={{ borderRadius: '8px' }}
                  src="https://open.spotify.com/embed/playlist/37i9dQZF1DZ06evO177mU2?utm_source=generator&theme=0"
                  width="100%" 
                  height="152" 
                  frameBorder="0"
                  allowFullScreen
                  allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" 
                  loading="lazy"
                  className="rounded-lg w-full"
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
