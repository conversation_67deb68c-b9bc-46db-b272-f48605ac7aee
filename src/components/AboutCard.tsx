import { motion } from 'framer-motion';
import { Cat, <PERSON>, Music } from 'lucide-react';
import React from 'react';

const AboutCard: React.FC = () => {
  return (
    <section id="about-section" className="py-8 md:py-16 px-4 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
              opacity: [0.1, 0.2, 0.1],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 10 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 3
            }}
            className="absolute w-24 h-24 border border-white/10 rounded-full"
            style={{
              left: `${15 + i * 30}%`,
              top: `${20 + i * 20}%`
            }}
          />
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        whileInView={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
        viewport={{ once: true }}
        className="max-w-4xl mx-auto relative z-10"
      >
        <motion.div
          className="bg-gradient-to-br from-white/25 via-rosa-pastel/20 to-purpura-magico/20 dark:from-white/15 dark:via-white/10 dark:to-white/5 backdrop-blur-xl rounded-3xl md:rounded-[2rem] p-8 md:p-10 lg:p-14 shadow-glow border border-white/40 animate-scale-pulse"
          whileHover={{
            scale: 1.02,
            boxShadow: "0 25px 50px rgba(255, 105, 180, 0.3)"
          }}
          transition={{ duration: 0.3 }}
        >
          <div className="text-center mb-8 md:mb-10">
            <motion.div
              animate={{
                rotate: [0, 15, -15, 0],
                scale: [1, 1.2, 1],
                y: [0, -5, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-5xl md:text-7xl mb-6 animate-bounce-gentle"
            >
              🐱
            </motion.div>
            <motion.h2
              className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6"
              style={{
                textShadow: '0 4px 8px rgba(0,0,0,0.6), 0 0 15px rgba(255,105,180,0.4)',
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
              }}
            >
              Sobre Vane
            </motion.h2>
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="font-inter text-base md:text-lg text-white/95 max-w-2xl mx-auto leading-relaxed animate-fade-in-up"
              style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
            >
              Una alma creativa que encuentra magia en los pequeños detalles de la vida
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mt-10 md:mt-14">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              whileHover={{
                scale: 1.08,
                y: -8,
                rotateY: 5,
                boxShadow: "0 20px 40px rgba(255, 105, 180, 0.3)"
              }}
              className="text-center p-6 md:p-8 bg-gradient-to-br from-white/20 to-rosa-pastel/20 rounded-2xl md:rounded-3xl backdrop-blur-lg border border-white/30 shadow-glow hover:shadow-glow-lg transition-all duration-300 group"
            >
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="text-oro-metalico mb-4 md:mb-6 flex justify-center animate-glow"
              >
                <Cat size={32} className="md:w-10 md:h-10 drop-shadow-lg group-hover:scale-110 transition-transform" />
              </motion.div>
              <h3 className="font-playfair text-lg md:text-xl font-bold text-white mb-3 animate-scale-pulse">
                Amante de los gatos
              </h3>
              <p className="font-inter text-white/90 text-sm md:text-base leading-relaxed">
                Cada gatito tiene una historia que contar 🐾
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              whileHover={{
                scale: 1.08,
                y: -8,
                rotateY: 5,
                boxShadow: "0 20px 40px rgba(138, 43, 226, 0.3)"
              }}
              className="text-center p-6 md:p-8 bg-gradient-to-br from-white/20 to-purpura-magico/20 rounded-2xl md:rounded-3xl backdrop-blur-lg border border-white/30 shadow-glow hover:shadow-glow-lg transition-all duration-300 group"
            >
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="text-rosa-neon mb-4 md:mb-6 flex justify-center animate-glow"
              >
                <Music size={32} className="md:w-10 md:h-10 drop-shadow-lg group-hover:scale-110 transition-transform" />
              </motion.div>
              <h3 className="font-playfair text-lg md:text-xl font-bold text-white mb-3 animate-scale-pulse">
                Música en el alma
              </h3>
              <p className="font-inter text-white/90 text-sm md:text-base leading-relaxed">
                Cada canción es un sentimiento 🎵
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              whileHover={{
                scale: 1.08,
                y: -8,
                rotateY: 5,
                boxShadow: "0 20px 40px rgba(255, 215, 0, 0.3)"
              }}
              className="text-center p-6 md:p-8 bg-gradient-to-br from-white/20 to-dorado-suave/20 rounded-2xl md:rounded-3xl backdrop-blur-lg border border-white/30 shadow-glow hover:shadow-glow-lg transition-all duration-300 group"
            >
              <motion.div
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.8, 1, 0.8]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="text-coral-suave mb-4 md:mb-6 flex justify-center animate-glow"
              >
                <Heart size={32} className="md:w-10 md:h-10 drop-shadow-lg group-hover:scale-110 transition-transform" fill="currentColor" />
              </motion.div>
              <h3 className="font-playfair text-lg md:text-xl font-bold text-white mb-3 animate-scale-pulse">
                Corazón kawaii
              </h3>
              <p className="font-inter text-white/90 text-sm md:text-base leading-relaxed">
                Encontrando belleza en lo simple 💖
              </p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default AboutCard;