import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Music, Cat } from 'lucide-react';

const AboutCard: React.FC = () => {
  return (
    <section id="about-section" className="py-8 md:py-16 px-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="max-w-4xl mx-auto"
      >
        <div className="bg-white/20 dark:bg-white/10 backdrop-blur-lg rounded-2xl md:rounded-3xl p-6 md:p-8 lg:p-12 shadow-2xl border border-white/30">
          <div className="text-center mb-6 md:mb-8">
            <motion.div
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-4xl md:text-6xl mb-4"
            >
              🐱
            </motion.div>
            <h2 className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
              Sobre Vane
            </h2>
            <p className="font-inter text-base md:text-lg text-white/90 max-w-2xl mx-auto leading-relaxed">
              Una alma creativa que encuentra magia en los pequeños detalles de la vida
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mt-8 md:mt-12">
            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              className="text-center p-4 md:p-6 bg-white/10 rounded-xl md:rounded-2xl backdrop-blur-sm border border-white/20"
            >
              <div className="text-dorado-suave mb-3 md:mb-4 flex justify-center">
                <Cat size={28} className="md:w-8 md:h-8" />
              </div>
              <h3 className="font-playfair text-lg md:text-xl font-semibold text-white mb-2">
                Amante de los gatos
              </h3>
              <p className="font-inter text-white/80 text-sm">
                Cada gatito tiene una historia que contar
              </p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              className="text-center p-4 md:p-6 bg-white/10 rounded-xl md:rounded-2xl backdrop-blur-sm border border-white/20"
            >
              <div className="text-dorado-suave mb-3 md:mb-4 flex justify-center">
                <Music size={28} className="md:w-8 md:h-8" />
              </div>
              <h3 className="font-playfair text-lg md:text-xl font-semibold text-white mb-2">
                Música en el alma
              </h3>
              <p className="font-inter text-white/80 text-sm">
                Cada canción es un sentimiento
              </p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              className="text-center p-4 md:p-6 bg-white/10 rounded-xl md:rounded-2xl backdrop-blur-sm border border-white/20"
            >
              <div className="text-dorado-suave mb-3 md:mb-4 flex justify-center">
                <Heart size={28} className="md:w-8 md:h-8" />
              </div>
              <h3 className="font-playfair text-lg md:text-xl font-semibold text-white mb-2">
                Corazón kawaii
              </h3>
              <p className="font-inter text-white/80 text-sm">
                Encontrando belleza en lo simple
              </p>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default AboutCard;