import { motion } from 'framer-motion';
import { Heart, Instagram, Star } from 'lucide-react';
import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="py-12 md:py-16 mt-12 md:mt-16 border-t border-white/20 px-4">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="container mx-auto text-center"
      >
        {/* Decorative stars */}
        <div className="flex justify-center gap-2 md:gap-4 mb-6 md:mb-8">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
            >
              <Star className="text-dorado-suave w-3 h-3 md:w-4 md:h-4" fill="currentColor" />
            </motion.div>
          ))}
        </div>

        <motion.h3
          whileHover={{ scale: 1.05 }}
          className="font-playfair text-2xl md:text-3xl font-bold text-white mb-3 md:mb-4 flex items-center justify-center gap-2"
        >
          Con cariño
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
          >
            <Heart className="text-red-300 w-6 h-6 md:w-7 md:h-7" fill="currentColor" />
          </motion.div>
        </motion.h3>

        <p className="font-inter text-white/80 mb-6 md:mb-8 max-w-md mx-auto text-sm md:text-base">
          Un rincón digital lleno de magia, música y momentos únicos 🌸
        </p>

        {/* Instagram link */}
        <div className="flex justify-center mb-6 md:mb-8">
          <motion.a
            href="https://www.instagram.com/vnemiaw/"
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.15, y: -3 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-pink-500 to-purple-600 p-4 md:p-5 rounded-full border border-white/30 text-white hover:from-pink-400 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl group"
          >
            <Instagram size={24} className="md:w-7 md:h-7 group-hover:rotate-12 transition-transform duration-300" />
          </motion.a>
        </div>

        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="font-inter text-white/90 text-sm md:text-base mb-4 font-medium"
        >
          Sígueme en Instagram ✨
        </motion.p>

        {/* Cat decoration */}
        <motion.div
          animate={{ 
            y: [0, -5, 0],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="text-3xl md:text-4xl mb-3 md:mb-4"
        >
          🐱
        </motion.div>

        <p className="font-inter text-white/60 text-xs md:text-sm max-w-lg mx-auto leading-relaxed">
          © 2025 𝑣𝑎𝑛𝑒𝑠𝑎 𝑚𝑎𝑐𝑎𝑟𝑒𝑛𝑎 - Hecho tomando mate <3
        </p>
      </motion.div>
    </footer>
  );
};

export default Footer;