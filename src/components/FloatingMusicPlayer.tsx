import { AnimatePresence, motion } from 'framer-motion';
import { Maximize2, Minimize2, Music, X } from 'lucide-react';
import React, { useState } from 'react';

const FloatingMusicPlayer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const togglePlayer = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setIsMinimized(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return (
    <>
      {/* Floating Music Button */}
      <motion.button
        onClick={togglePlayer}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        className="fixed bottom-6 right-6 z-50 bg-gradient-to-r from-rosa-brillante to-purpura-magico p-4 rounded-full shadow-glow hover:shadow-glow-lg transition-all duration-300 border border-white/30"
        style={{
          background: 'linear-gradient(45deg, rgba(255, 105, 180, 0.9), rgba(138, 43, 226, 0.9))',
          backdropFilter: 'blur(10px)'
        }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <Music className="text-white w-6 h-6" />
        </motion.div>
      </motion.button>

      {/* Floating Music Player */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
            className="fixed bottom-24 right-6 z-40 bg-gradient-to-br from-white/25 to-rosa-pastel/25 rounded-2xl border border-white/40 shadow-lg overflow-hidden"
            style={{
              backdropFilter: 'blur(8px)',
              width: isMinimized ? '280px' : '350px'
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-rosa-brillante/20 to-purpura-magico/20 border-b border-white/20">
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <Music className="text-white w-5 h-5" />
                </motion.div>
                <span className="text-white font-medium text-sm">Música que Vibra</span>
              </div>
              
              <div className="flex items-center gap-2">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleMinimize}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  {isMinimized ? (
                    <Maximize2 size={16} />
                  ) : (
                    <Minimize2 size={16} />
                  )}
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={togglePlayer}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  <X size={16} />
                </motion.button>
              </div>
            </div>

            {/* Spotify Player */}
            <AnimatePresence>
              {!isMinimized && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4"
                >
                  <iframe 
                    style={{ borderRadius: '12px' }}
                    src="https://open.spotify.com/embed/playlist/37i9dQZF1DZ06evO177mU2?utm_source=generator&theme=0"
                    width="100%" 
                    height="152" 
                    frameBorder="0"
                    allowFullScreen
                    allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" 
                    loading="lazy"
                    className="rounded-xl"
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default FloatingMusicPlayer;
