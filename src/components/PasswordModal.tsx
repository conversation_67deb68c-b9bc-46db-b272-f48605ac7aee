import { AnimatePresence, motion } from 'framer-motion';
import { Lock, X } from 'lucide-react';
import React, { useState } from 'react';

interface PasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const PasswordModal: React.FC<PasswordModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Simular un pequeño delay para mejor UX
    await new Promise(resolve => setTimeout(resolve, 500));

    const correctPassword = import.meta.env.VITE_GALLERY_PASS || 'vane';
    
    if (password === correctPassword) {
      onSuccess();
      onClose();
    } else {
      setError('Contraseña incorrecta. ¿Estás seguro que eres VIP? 💕');
    }
    
    setIsLoading(false);
    setPassword('');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="bg-gradient-to-br from-rosa-pastel to-fucsia-suave p-6 md:p-8 rounded-2xl md:rounded-3xl shadow-2xl max-w-md w-full mx-4 border border-white/30"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4 md:mb-6">
              <h2 className="font-playfair text-xl md:text-2xl font-bold text-white flex items-center gap-2">
                <Lock size={20} className="md:w-6 md:h-6" />
                Acceso VIP
              </h2>
              <button
                onClick={onClose}
                className="text-white/70 hover:text-white transition-colors"
              >
                <X size={20} className="md:w-6 md:h-6" />
              </button>
            </div>

            <p className="font-inter text-white/90 mb-4 md:mb-6 text-center text-sm md:text-base">
              Esta galería es especial. Solo para ojos que saben apreciar el arte ✨
            </p>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Contraseña secreta..."
                  className="w-full px-4 py-3 rounded-xl md:rounded-2xl bg-white/30 border border-white/40 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/60 focus:bg-white/40 transition-all text-sm md:text-base"
                  style={{
                    textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                    backdropFilter: 'blur(4px)'
                  }}
                  disabled={isLoading}
                />
              </div>

              {error && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-red-200 text-xs md:text-sm mb-4 text-center"
                >
                  {error}
                </motion.p>
              )}

              <button
                type="submit"
                disabled={!password || isLoading}
                className="w-full bg-white/20 hover:bg-white/30 disabled:bg-white/10 text-white py-3 rounded-xl md:rounded-2xl font-semibold transition-all border border-white/30 disabled:cursor-not-allowed text-sm md:text-base"
              >
                {isLoading ? 'Verificando...' : 'Entrar al paraíso 💫'}
              </button>
            </form>

            <p className="font-inter text-white/60 text-xs text-center mt-3 md:mt-4">
              Pista: Empieza con "v" y termina con "e" 😉
            </p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PasswordModal;