import React, { useState } from 'react';
import { motion } from 'framer-motion';
import PasswordModal from './PasswordModal';

const PrivateGallery: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);

  // Placeholder images - en producción estas serían las fotos reales
  const privateImages = [
    'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=600&fit=crop',
    'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=400&h=500&fit=crop',
    'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop',
    'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=500&fit=crop',
    'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400&h=400&fit=crop',
  ];

  const handleAccessClick = () => {
    setShowModal(true);
  };

  const handleAccessGranted = () => {
    setHasAccess(true);
  };

  if (!hasAccess) {
    return (
      <>
        <section className="py-8 md:py-16 text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <h2 className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6">
              "Eres Arte" 🎨
            </h2>
            <p className="font-inter text-base md:text-lg text-white/90 mb-6 md:mb-8">
              Una galería privada donde cada imagen cuenta una historia única
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleAccessClick}
              className="bg-fucsia-suave hover:bg-pink-500 text-white px-8 md:px-12 py-3 md:py-4 rounded-full text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Acceso VIP ✨
            </motion.button>
          </motion.div>
        </section>

        <PasswordModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onSuccess={handleAccessGranted}
        />
      </>
    );
  }

  return (
    <section className="py-8 md:py-16 px-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-8 md:mb-12"
      >
        <h2 className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
          "Eres Arte" 🎨
        </h2>
        <p className="font-inter text-base md:text-lg text-white/90 max-w-2xl mx-auto">
          Cada momento capturado es una obra maestra
        </p>
      </motion.div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {privateImages.map((src, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            whileHover={{ scale: 1.03 }}
            className="relative group overflow-hidden rounded-xl md:rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20"
          >
            <img
              src={src}
              alt={`Arte ${index + 1}`}
              className="w-full aspect-square object-cover"
            />
            
            {/* Overlay con mensaje */}
            <motion.div
              initial={{ opacity: 0 }}
              whileHover={{ opacity: 1 }}
              className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex items-end justify-center p-4 md:p-6"
            >
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                whileHover={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="font-playfair text-white text-base md:text-lg font-semibold text-center"
              >
                Eres hermosa ✨
              </motion.p>
            </motion.div>

            {/* Decorative corner */}
            <div className="absolute top-3 md:top-4 right-3 md:right-4 text-dorado-suave opacity-60">
              <span className="text-xl md:text-2xl">💖</span>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="text-center mt-8 md:mt-12 px-4"
      >
        <p className="font-inter text-white/80 text-base md:text-lg italic">
          "La belleza está en los ojos del que mira, y tú eres pura belleza" 💫
        </p>
      </motion.div>
    </section>
  );
};

export default PrivateGallery;