import { motion } from 'framer-motion';
import React, { useState } from 'react';
import PasswordModal from './PasswordModal';

interface PrivateGalleryProps {
  onAccessGranted?: (granted: boolean) => void;
}

const PrivateGallery: React.FC<PrivateGalleryProps> = ({ onAccessGranted }) => {
  const [showModal, setShowModal] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);

  // Imágenes reales desde la carpeta public
  const privateImages = [
    '/IMG_2160.jpg',
    '/IMG_2161.jpg',
    '/IMG_2162.jpg',
    '/IMG_2163.jpg',
    '/IMG_2164.jpg',
    '/IMG_2166.jpg',
    '/IMG_2167.jpg',
    '/IMG_2168.PNG',
    '/IMG_2169.jpg',
    '/IMG_2170.PNG',
    '/IMG_2171.jpg',
    '/IMG_2172.jpg',
    '/IMG_2173.jpg',
    '/IMG_2174.jpg',
    '/IMG_2175.jpg',
    '/IMG_2176.jpg',
    '/IMG_2177.jpg',
    '/IMG_2178.jpg',
    '/IMG_2179.jpg',
    '/IMG_2180.jpg',
    '/IMG_2181.jpg',
    '/IMG_2182.jpg',
    '/IMG_2183.jpg',
    '/IMG_2184.jpg',
    '/IMG_2185.jpg',
    '/IMG_2186.jpg',
    '/IMG_2187.jpg',
    '/IMG_2188.jpg',
    '/IMG_2189.jpg',
    '/IMG_2190.PNG',
    '/IMG_2191.jpg',
    '/IMG_2192.jpg',
    '/IMG_2193.jpg',
    '/IMG_2195.jpg',
  ];

  const handleAccessClick = () => {
    // Mostrar el modal directamente con la animación del botón
    setShowModal(true);
  };

  const handleAccessGranted = () => {
    setHasAccess(true);
    onAccessGranted?.(true);
  };

  return (
    <>
      <section className="py-8 md:py-16 text-center px-4 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.2, 0.1]
              }}
              transition={{
                duration: 15 + i * 3,
                repeat: Infinity,
                ease: "linear",
                delay: i * 2
              }}
              className="absolute w-32 h-32 border border-white/10 rounded-full"
              style={{
                left: `${20 + i * 20}%`,
                top: `${10 + i * 15}%`
              }}
            />
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          viewport={{ once: true }}
          className="max-w-2xl mx-auto relative z-10"
        >
          <motion.h2
            className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6"
            style={{
              textShadow: '0 4px 8px rgba(0,0,0,0.6), 0 0 15px rgba(255,105,180,0.4)',
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
            }}
          >
            "Eres Arte" 🎨
          </motion.h2>

          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="font-inter text-base md:text-lg text-white/95 mb-6 md:mb-8 animate-fade-in-up"
            style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
          >
            Una galería privada donde cada imagen cuenta una historia única
          </motion.p>

          <motion.button
            whileHover={{
              scale: 1.08,
              y: -3,
              boxShadow: "0 15px 30px rgba(255, 105, 180, 0.4)"
            }}
            whileTap={{ scale: 0.95 }}
            onClick={handleAccessClick}
            className="bg-gradient-to-r from-fucsia-suave to-rosa-brillante hover:from-rosa-brillante hover:to-purpura-magico text-white px-10 md:px-14 py-4 md:py-5 rounded-full text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 border border-white/30 group"
            style={{
              textShadow: '0 1px 2px rgba(0,0,0,0.3)'
            }}
          >
            <span className="flex items-center gap-2">
              Acceso VIP
              <motion.span
                animate={{ rotate: [0, 15, -15, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="group-hover:scale-110 transition-transform"
              >
                ✨
              </motion.span>
            </span>
          </motion.button>
        </motion.div>
      </section>

      {hasAccess && (
        <section className="py-8 md:py-16 px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
            className="text-center mb-8 md:mb-12"
          >
            <motion.h3
              className="font-playfair text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-4"
              style={{
                textShadow: '0 4px 8px rgba(0,0,0,0.6), 0 0 15px rgba(255,215,0,0.4)',
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
              }}
            >
              Bienvenida al paraíso ✨
            </motion.h3>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="font-inter text-base md:text-lg text-white/95 max-w-2xl mx-auto animate-fade-in-up"
              style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
            >
              Cada momento capturado es una obra maestra
            </motion.p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {privateImages.map((src, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8, y: 30 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.08,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  rotateY: 5,
                  boxShadow: "0 20px 40px rgba(255, 105, 180, 0.3)"
                }}
                className="relative group overflow-hidden rounded-2xl md:rounded-3xl bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-lg border border-white/30 shadow-glow hover:shadow-glow-lg transition-all duration-300"
                style={{
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,105,180,0.1), rgba(138,43,226,0.1))'
                }}
              >
                <img
                  src={src}
                  alt={`Arte ${index + 1}`}
                  className="w-full aspect-square object-cover transition-transform duration-500 group-hover:scale-110"
                />

                {/* Enhanced decorative corner */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute top-3 md:top-4 right-3 md:right-4 text-oro-metalico opacity-80 animate-glow"
                >
                  <span className="text-xl md:text-2xl drop-shadow-lg">💖</span>
                </motion.div>

                {/* Additional sparkle effect */}
                <motion.div
                  animate={{
                    opacity: [0.3, 0.8, 0.3],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: index * 0.2
                  }}
                  className="absolute top-2 left-2 text-lavanda opacity-60"
                >
                  <span className="text-sm">✨</span>
                </motion.div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 1.5, duration: 0.8, type: "spring", stiffness: 100 }}
            className="text-center mt-12 md:mt-16 px-4"
          >
            <motion.p
              className="font-inter text-white/95 text-lg md:text-xl italic font-medium"
              style={{
                textShadow: '0 4px 8px rgba(0,0,0,0.6), 0 0 10px rgba(255,215,0,0.3)',
                filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'
              }}
            >
              "La belleza está en los ojos del que mira, y tú eres pura belleza"
              <motion.span
                animate={{ rotate: [0, 15, -15, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="inline-block ml-2"
              >
                💫
              </motion.span>
            </motion.p>
          </motion.div>
        </section>
      )}

      <PasswordModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={handleAccessGranted}
      />
    </>
  );
};

export default PrivateGallery;