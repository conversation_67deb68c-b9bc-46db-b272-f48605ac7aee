import React from 'react';
import { motion } from 'framer-motion';
import { Music, Headphones } from 'lucide-react';

const MusicSection: React.FC = () => {
  return (
    <section className="py-8 md:py-16 px-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="text-center mb-8 md:mb-12"
      >
        <div className="flex justify-center items-center gap-2 md:gap-4 mb-4">
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Music className="text-dorado-suave" size={32} />
          </motion.div>
          <h2 className="font-playfair text-3xl md:text-4xl lg:text-5xl font-bold text-white text-center">
            Música que Vibra
          </h2>
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <Headphones className="text-dorado-suave" size={32} />
          </motion.div>
        </div>
        <p className="font-inter text-base md:text-lg text-white/90 max-w-2xl mx-auto px-4">
          Los ritmos que acompañan cada momento especial 🎵
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        viewport={{ once: true }}
        className="max-w-4xl mx-auto px-4"
      >
        <motion.div
          animate={{ 
            y: [0, -8, 0],
            rotateX: [0, 2, 0],
            rotateY: [0, 1, 0]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          whileHover={{ 
            scale: 1.02,
            y: -12,
            transition: { duration: 0.3 }
          }}
          className="bg-white/10 backdrop-blur-lg rounded-2xl md:rounded-3xl p-4 md:p-6 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300"
          style={{
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
          }}
        >
          <motion.div
            whileHover={{ scale: 1.01 }}
            transition={{ duration: 0.2 }}
            className="w-full"
          >
            <iframe 
              style={{ borderRadius: '12px' }}
              src="https://open.spotify.com/embed/playlist/37i9dQZF1DZ06evO177mU2?utm_source=generator&theme=0"
              width="100%" 
              height="352" 
              frameBorder="0"
              allowFullScreen
              allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" 
              loading="lazy"
              className="rounded-xl w-full min-h-[300px] md:min-h-[352px]"
            />
          </motion.div>
        </motion.div>
        
        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="font-inter text-white/80 text-center mt-4 md:mt-6 italic text-sm md:text-base px-4"
        >
          "La música es el lenguaje del alma" ✨
        </motion.p>
      </motion.div>
    </section>
  );
};

export default MusicSection;