import React from 'react';
import { motion } from 'framer-motion';
import { Moon, Star, Sparkles } from 'lucide-react';

interface HeroProps {
  onButtonClick: () => void;
}

const Hero: React.FC<HeroProps> = ({ onButtonClick }) => {
  const scrollToNext = () => {
    const nextSection = document.querySelector('#about-section');
    nextSection?.scrollIntoView({ behavior: 'smooth' });
    onButtonClick();
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden px-4">
      {/* Floating decorative elements - responsive positioning */}
      <motion.div
        animate={{ 
          rotate: [0, 360],
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }}
        className="absolute top-16 md:top-20 left-4 md:left-20 text-dorado-suave opacity-60"
      >
        <Star size={24} className="md:w-8 md:h-8" />
      </motion.div>
      
      <motion.div
        animate={{ 
          y: [-10, 10, -10],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-24 md:top-32 right-4 md:right-32 text-dorado-suave opacity-70"
      >
        <Moon size={32} className="md:w-10 md:h-10" />
      </motion.div>

      <motion.div
        animate={{ 
          scale: [1, 1.2, 1],
          opacity: [0.4, 0.8, 0.4]
        }}
        transition={{ 
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute bottom-24 md:bottom-32 left-4 md:left-32 text-dorado-suave"
      >
        <Sparkles size={20} className="md:w-7 md:h-7" />
      </motion.div>

      {/* Main content */}
      <div className="text-center z-10 max-w-4xl mx-auto">
        <motion.h1
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="font-playfair text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold text-white mb-6 md:mb-8 drop-shadow-lg leading-tight"
          style={{ textShadow: '0 4px 8px rgba(0,0,0,0.3)' }}
        >
          𝑣𝑎𝑛𝑒𝑠𝑎 𝑚𝑎𝑐𝑎𝑟𝑒𝑛𝑎 ☽✯
        </motion.h1>
        
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="font-inter text-lg sm:text-xl md:text-2xl text-white/90 mb-8 md:mb-12 max-w-2xl mx-auto px-4 leading-relaxed"
        >
          Un espacio mágico lleno de color, música y momentos especiales ✨
        </motion.p>

        <motion.button
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={scrollToNext}
          className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white px-8 md:px-10 py-3 md:py-4 rounded-full text-base md:text-lg font-semibold border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Descubre ✨
        </motion.button>
      </div>

      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/10" />
    </section>
  );
};

export default Hero;