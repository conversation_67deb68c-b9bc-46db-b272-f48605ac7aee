import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import React from 'react';

interface HeroProps {
  onButtonClick: () => void;
}

const Hero: React.FC<HeroProps> = ({ onButtonClick }) => {
  const scrollToNext = () => {
    const nextSection = document.querySelector('#about-section');
    nextSection?.scrollIntoView({ behavior: 'smooth' });
    onButtonClick();
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden px-4">
      {/* Enhanced floating decorative elements */}
      <motion.div
        animate={{
          rotate: [0, 360],
          scale: [1, 1.2, 1],
          x: [0, 10, 0],
          y: [0, -5, 0]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-16 md:top-20 left-4 md:left-20 text-oro-metalico opacity-80 animate-glow"
      >
        <Star size={28} className="md:w-10 md:h-10 drop-shadow-lg" fill="currentColor" />
      </motion.div>

      <motion.div
        animate={{
          y: [-15, 15, -15],
          rotate: [0, 10, -10, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-24 md:top-32 right-4 md:right-32 text-lavanda opacity-90 animate-pulse-soft"
      >
        <Moon size={36} className="md:w-12 md:h-12 drop-shadow-lg" fill="currentColor" />
      </motion.div>

      <motion.div
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.5, 1, 0.5],
          rotate: [0, 180, 360]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute bottom-24 md:bottom-32 left-4 md:left-32 text-rosa-neon"
      >
        <Sparkles size={24} className="md:w-8 md:h-8 drop-shadow-lg" />
      </motion.div>

      {/* Additional floating elements */}
      <motion.div
        animate={{
          x: [0, 20, 0],
          y: [0, -10, 0],
          opacity: [0.3, 0.7, 0.3]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
        className="absolute top-1/3 right-8 md:right-16 text-coral-suave"
      >
        <Star size={16} className="md:w-6 md:h-6" fill="currentColor" />
      </motion.div>

      <motion.div
        animate={{
          x: [0, -15, 0],
          y: [0, 8, 0],
          rotate: [0, -45, 0]
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
        className="absolute bottom-1/3 right-12 md:right-24 text-purpura-magico opacity-60"
      >
        <Sparkles size={18} className="md:w-7 md:h-7" />
      </motion.div>

      {/* Enhanced main content */}
      <div className="text-center z-10 max-w-4xl mx-auto">
        <motion.h1
          initial={{ opacity: 0, y: -50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 1.2,
            delay: 0.2,
            type: "spring",
            stiffness: 100
          }}
          className="font-playfair text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold text-white mb-6 md:mb-8 drop-shadow-2xl leading-tight animate-scale-pulse"
          style={{
            textShadow: '0 8px 16px rgba(0,0,0,0.4), 0 0 30px rgba(255,255,255,0.1)',
            background: 'linear-gradient(45deg, #ffffff, #FFD700, #FF69B4, #ffffff)',
            backgroundSize: '300% 300%',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            animation: 'shimmer 3s ease-in-out infinite'
          }}
        >
          𝑣𝑎𝑛𝑒𝑠𝑎 𝑚𝑎𝑐𝑎𝑟𝑒𝑛𝑎 ☽✯
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="font-inter text-lg sm:text-xl md:text-2xl text-white/95 mb-8 md:mb-12 max-w-2xl mx-auto px-4 leading-relaxed animate-fade-in-up"
          style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
        >
          Un espacio mágico lleno de color, música y momentos especiales ✨
        </motion.p>

        <motion.button
          initial={{ opacity: 0, y: 30, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 0.8,
            delay: 1,
            type: "spring",
            stiffness: 200
          }}
          whileHover={{
            scale: 1.08,
            y: -2,
            boxShadow: "0 10px 25px rgba(255, 110, 161, 0.4)"
          }}
          whileTap={{ scale: 0.95 }}
          onClick={scrollToNext}
          className="bg-gradient-to-r from-rosa-brillante/30 to-purpura-magico/30 backdrop-blur-lg hover:from-rosa-brillante/40 hover:to-purpura-magico/40 text-white px-10 md:px-12 py-4 md:py-5 rounded-full text-base md:text-lg font-semibold border border-white/40 shadow-glow hover:shadow-glow-lg transition-all duration-300 animate-bounce-gentle"
          style={{
            background: 'linear-gradient(45deg, rgba(255, 105, 180, 0.3), rgba(138, 43, 226, 0.3))',
            backdropFilter: 'blur(20px)'
          }}
        >
          <span className="flex items-center gap-2">
            Descubre ✨
            <motion.span
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              🌟
            </motion.span>
          </span>
        </motion.button>
      </div>

      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-rosa-pastel/5 to-purpura-magico/10" />

      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 1.5
            }}
            className="absolute w-2 h-2 bg-white/20 rounded-full blur-sm"
            style={{
              left: `${10 + i * 15}%`,
              top: `${20 + i * 10}%`
            }}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;