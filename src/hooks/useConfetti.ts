import { useCallback } from 'react';
import confetti from 'canvas-confetti';

export const useConfetti = () => {
  const triggerConfetti = useCallback(() => {
    const colors = ['#F8B5C8', '#FF6EA1', '#FBD784', '#FFF0F5'];
    
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: colors,
      shapes: ['star', 'circle'],
      scalar: 1.2,
      drift: 0.5,
      gravity: 0.8,
      ticks: 200,
    });

    // Segundo burst con delay
    setTimeout(() => {
      confetti({
        particleCount: 50,
        spread: 50,
        origin: { x: 0.3, y: 0.7 },
        colors: colors,
        shapes: ['star'],
        scalar: 0.8,
      });
    }, 300);

    // Tercer burst
    setTimeout(() => {
      confetti({
        particleCount: 50,
        spread: 50,
        origin: { x: 0.7, y: 0.7 },
        colors: colors,
        shapes: ['circle'],
        scalar: 0.8,
      });
    }, 600);
  }, []);

  return { triggerConfetti };
};