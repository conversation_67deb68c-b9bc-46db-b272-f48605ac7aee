import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import AboutCard from './components/AboutCard';
import DarkModeToggle from './components/DarkModeToggle';
import Footer from './components/Footer';
import Header from './components/Header';
import Hero from './components/Hero';
import PrivateGallery from './components/PrivateGallery';
import PublicGallery from './components/PublicGallery';
import { useConfetti } from './hooks/useConfetti';

function HomePage() {
  const [showPrivateGallery, setShowPrivateGallery] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { triggerConfetti } = useConfetti();

  const handlePrivateAccess = (granted: boolean) => {
    setShowPrivateGallery(granted);
    if (granted) {
      triggerConfetti();
    }
  };

  return (
    <div className={`min-h-screen transition-all duration-500 ${
      isDarkMode ? 'dark bg-gradient-to-br from-lila-oscuro to-purple-900' : 'bg-gradient-to-br from-rosa-pastel to-fucsia-suave'
    }`}>
      <Header />
      <DarkModeToggle isDarkMode={isDarkMode} onToggle={setIsDarkMode} />

      {/* Add padding to account for fixed header */}
      <div className="pt-20 md:pt-24">
        <Hero onButtonClick={triggerConfetti} />
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="container mx-auto space-y-8 md:space-y-16"
      >
        <AboutCard />
        <PublicGallery />
        
        <AnimatePresence mode="wait">
          {!showPrivateGallery ? (
            <motion.div
              key="private-access"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="text-center px-4"
            >
              <button
                onClick={() => handlePrivateAccess(true)}
                className="bg-fucsia-suave hover:bg-pink-500 text-white px-8 md:px-12 py-3 md:py-4 rounded-full text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                Acceso VIP ✨
              </button>
            </motion.div>
          ) : (
            <PrivateGallery key="private-gallery" />
          )}
        </AnimatePresence>
      </motion.div>
      
      <Footer />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/galeria" element={<HomePage />} />
        <Route path="/musica" element={<HomePage />} />
      </Routes>
    </Router>
  );
}

export default App;