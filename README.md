# 𝑣𝑎𝑛𝑒𝑠𝑎 𝑚𝑎𝑐𝑎𝑟𝑒𝑛𝑎 ☽✯

Un sitio web personal mágico y kawaii, diseñado con amor y atención a cada detalle.

## 🌸 Características

- **Diseño Responsivo**: Perfecto en todos los dispositivos
- **Animaciones Suaves**: Implementadas con Framer Motion
- **Galería Privada**: Protegida con contraseña personalizable
- **Música Integrada**: Playlist de Spotify embebida
- **Efectos Confetti**: Celebraciones mágicas con canvas-confetti
- **Modo Noche**: Alterna entre temas claro y oscuro
- **Glassmorphism**: Efectos de cristal modernos

## 🛠️ Stack Tecnológico

- **Vite** - Bundler rápido y moderno
- **React 18** - Framework frontend
- **TypeScript** - Tipado estático
- **Tailwind CSS** - Estilos utilitarios
- **Framer Motion** - Animaciones fluidas
- **<PERSON>vas Confetti** - Efectos de celebración
- **React Router** - Navegación SPA
- **Lucide React** - Iconos hermosos

## 🚀 Instalación y Desarrollo

```bash
# Clonar el repositorio
git clone <tu-repo>
cd vanesa-macarena-website

# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev

# Construir para producción
npm run build

# Preview de producción
npm run preview
```

## 🎨 Personalización

### Cambiar la Contraseña de la Galería Privada

1. Crear archivo `.env` en la raíz del proyecto:
```env
VITE_GALLERY_PASS=tu_nueva_contraseña
```

2. La contraseña por defecto es `vane`

### Cambiar las Fotos Privadas

1. Reemplaza las URLs en `src/components/PrivateGallery.tsx`
2. O coloca tus imágenes en `src/assets/private/` e importalas

### Personalizar Colores

Edita `tailwind.config.js` para cambiar la paleta:

```js
colors: {
  'rosa-pastel': '#F8B5C8',
  'fucsia-suave': '#FF6EA1', 
  'crema': '#FFF0F5',
  'dorado-suave': '#FBD784',
  'lila-oscuro': '#6B46C1',
}
```

### Cambiar la Playlist de Spotify

En `src/components/MusicSection.tsx`, reemplaza la URL del iframe con tu playlist.

## 📁 Estructura del Proyecto

```
src/
├── components/           # Componentes React
│   ├── Hero.tsx         # Sección principal con título
│   ├── AboutCard.tsx    # Tarjeta sobre Vane
│   ├── PublicGallery.tsx # Galería pública kawaii
│   ├── PasswordModal.tsx # Modal de contraseña
│   ├── PrivateGallery.tsx # Galería privada "Eres Arte"
│   ├── MusicSection.tsx  # Sección de música
│   ├── Footer.tsx       # Pie de página
│   └── DarkModeToggle.tsx # Toggle modo oscuro
├── hooks/               # Hooks personalizados
│   └── useConfetti.ts   # Hook para efectos confetti
├── App.tsx             # Componente principal
├── main.tsx            # Punto de entrada
└── index.css           # Estilos globales
```

## 🎯 Funcionalidades Destacadas

### Galería Privada
- Protegida con contraseña personalizable
- Overlay "Eres hermosa" que aparece al hacer hover
- Animaciones de entrada escalonadas

### Efectos Confetti
- Se activan al hacer clic en botones importantes
- Colores personalizados que coinciden con la paleta
- Múltiples ráfagas con delays para mayor impacto

### Modo Noche
- Transición suave entre temas claro y oscuro
- Iconos animados (luna/sol) en el toggle
- Colores adaptados para ambos modos

### Animaciones
- Elementos flotantes en el hero
- Entrada progresiva de elementos
- Hover effects en galerías e interacciones

## 🔧 Scripts Disponibles

- `npm run dev` - Servidor de desarrollo
- `npm run build` - Construir para producción  
- `npm run preview` - Preview de la build
- `npm run lint` - Linter ESLint

## 💫 Próximas Mejoras

- [ ] Sistema de comentarios
- [ ] Más efectos de partículas
- [ ] Galería de videos
- [ ] Integración con redes sociales
- [ ] PWA (Progressive Web App)

---

**Hecho con 💖 y muchas tazas de café para una persona muy especial**