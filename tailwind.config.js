/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'rosa-pastel': '#F8B5C8',
        'fucsia-suave': '#FF6EA1',
        'crema': '#FFF0F5',
        'dorado-suave': '#FBD784',
        'lila-oscuro': '#6B46C1',
        'rosa-brillante': '#FF69B4',
        'purpura-magico': '#8A2BE2',
        'coral-suave': '#FF7F7F',
        'lavanda': '#E6E6FA',
        'oro-metalico': '#FFD700',
        'rosa-neon': '#FF1493',
      },
      fontFamily: {
        'playfair': ['Playfair Display', 'serif'],
        'inter': ['Inter', 'sans-serif'],
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
        'pulse-soft': 'pulse-soft 2s infinite',
        'bounce-gentle': 'bounce-gentle 2s infinite',
        'shimmer': 'shimmer 2.5s infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'slide-up': 'slide-up 0.6s ease-out',
        'fade-in-up': 'fade-in-up 0.8s ease-out',
        'rotate-slow': 'rotate-slow 8s linear infinite',
        'scale-pulse': 'scale-pulse 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'pulse-soft': {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.8 },
        },
        'bounce-gentle': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(255, 110, 161, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(255, 110, 161, 0.8), 0 0 30px rgba(255, 110, 161, 0.6)' },
        },
        'slide-up': {
          '0%': { transform: 'translateY(30px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        'fade-in-up': {
          '0%': { transform: 'translateY(40px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        'rotate-slow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'scale-pulse': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
      boxShadow: {
        'glow': '0 0 20px rgba(255, 110, 161, 0.5)',
        'glow-lg': '0 0 30px rgba(255, 110, 161, 0.6)',
        'inner-glow': 'inset 0 0 20px rgba(255, 255, 255, 0.1)',
      },
    },
  },
  plugins: [],
  darkMode: 'class',
};